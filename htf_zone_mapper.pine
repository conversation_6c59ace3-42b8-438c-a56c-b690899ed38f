//@version=5
strategy("HTF Zone Mapper - Multi-Timeframe Strategy", shorttitle="HTF-ZM", overlay=true,
         default_qty_type=strategy.percent_of_equity, default_qty_value=1,
         initial_capital=10000, commission_type=strategy.commission.percent, commission_value=0.1)

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📊 INPUT PARAMETERS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Timeframe Settings
htf_timeframe = input.timeframe("60", "HTF Timeframe (Higher)", group="🕐 Timeframe Settings")
ltf_timeframe = input.timeframe("5", "LTF Timeframe (Lower)", group="🕐 Timeframe Settings")

// EMA Settings
ema_fast = input.int(9, "EMA Fast Period", minval=1, group="📈 EMA Settings")
ema_slow = input.int(21, "EMA Slow Period", minval=1, group="📈 EMA Settings")

// Swing Detection Settings
htf_swing_length = input.int(10, "HTF Swing Length", minval=3, maxval=50, group="🔍 Swing Detection")
ltf_swing_length = input.int(5, "LTF Swing Length", minval=3, maxval=20, group="🔍 Swing Detection")

// Zone Settings
zone_tolerance = input.float(0.1, "S&R Zone Tolerance (%)", minval=0.01, maxval=1.0, step=0.01, group="🎯 Zone Settings")
zone_extend = input.int(50, "Zone Extension (bars)", minval=10, maxval=200, group="🎯 Zone Settings")

// Pullback Settings
pullback_min = input.float(38.2, "Pullback Min (%)", minval=20.0, maxval=80.0, step=0.1, group="📊 Pullback Settings")
pullback_max = input.float(61.8, "Pullback Max (%)", minval=20.0, maxval=80.0, step=0.1, group="📊 Pullback Settings")

// Risk Management
risk_percent = input.float(1.0, "Risk per Trade (%)", minval=0.1, maxval=5.0, step=0.1, group="💰 Risk Management")
risk_reward = input.float(2.0, "Risk:Reward Ratio", minval=1.0, maxval=5.0, step=0.1, group="💰 Risk Management")
atr_multiplier = input.float(1.5, "ATR Multiplier for SL", minval=0.5, maxval=3.0, step=0.1, group="💰 Risk Management")

// Visual Settings
show_htf_zones = input.bool(true, "Show HTF Zones", group="🎨 Visual Settings")
show_ltf_structure = input.bool(true, "Show LTF Structure", group="🎨 Visual Settings")
show_info_table = input.bool(true, "Show Info Table", group="🎨 Visual Settings")
show_background = input.bool(true, "Show Background Colors", group="🎨 Visual Settings")

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📊 HELPER FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Calculate ATR for stop loss
atr_value = ta.atr(14)

// EMA calculations for both timeframes
htf_ema_fast = request.security(syminfo.tickerid, htf_timeframe, ta.ema(close, ema_fast))
htf_ema_slow = request.security(syminfo.tickerid, htf_timeframe, ta.ema(close, ema_slow))
ltf_ema_fast = ta.ema(close, ema_fast)
ltf_ema_slow = ta.ema(close, ema_slow)

// HTF and LTF bias
htf_bullish = htf_ema_fast > htf_ema_slow
htf_bearish = htf_ema_fast < htf_ema_slow
ltf_bullish = ltf_ema_fast > ltf_ema_slow
ltf_bearish = ltf_ema_fast < ltf_ema_slow

// Trend alignment
trend_aligned_bull = htf_bullish and ltf_bullish
trend_aligned_bear = htf_bearish and ltf_bearish

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔍 SWING POINT DETECTION
// ═══════════════════════════════════════════════════════════════════════════════════════

// HTF Swing Points
htf_high = request.security(syminfo.tickerid, htf_timeframe, high)
htf_low = request.security(syminfo.tickerid, htf_timeframe, low)

htf_swing_high = ta.pivothigh(htf_high, htf_swing_length, htf_swing_length)
htf_swing_low = ta.pivotlow(htf_low, htf_swing_length, htf_swing_length)

// LTF Swing Points
ltf_swing_high = ta.pivothigh(high, ltf_swing_length, ltf_swing_length)
ltf_swing_low = ta.pivotlow(low, ltf_swing_length, ltf_swing_length)

// Store swing points
var float last_htf_swing_high = na
var float last_htf_swing_low = na
var float last_ltf_swing_high = na
var float last_ltf_swing_low = na

if not na(htf_swing_high)
    last_htf_swing_high := htf_swing_high
if not na(htf_swing_low)
    last_htf_swing_low := htf_swing_low
if not na(ltf_swing_high)
    last_ltf_swing_high := ltf_swing_high
if not na(ltf_swing_low)
    last_ltf_swing_low := ltf_swing_low

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎯 HTF ZONE MAPPING
// ═══════════════════════════════════════════════════════════════════════════════════════

// Calculate zone tolerance in price
zone_tolerance_price = close * zone_tolerance / 100

// HTF Support and Resistance Zones
var line htf_resistance_line = na
var line htf_support_line = na
var box htf_resistance_zone = na
var box htf_support_zone = na

// Update HTF zones
if not na(last_htf_swing_high) and show_htf_zones
    if not na(htf_resistance_line)
        line.delete(htf_resistance_line)
    if not na(htf_resistance_zone)
        box.delete(htf_resistance_zone)

    htf_resistance_line := line.new(bar_index - zone_extend, last_htf_swing_high,
                                   bar_index + zone_extend, last_htf_swing_high,
                                   color=color.red, style=line.style_dashed, width=2)

    htf_resistance_zone := box.new(bar_index - zone_extend, last_htf_swing_high - zone_tolerance_price,
                                  bar_index + zone_extend, last_htf_swing_high + zone_tolerance_price,
                                  bgcolor=color.new(color.red, 90), border_color=color.red, border_width=1)

if not na(last_htf_swing_low) and show_htf_zones
    if not na(htf_support_line)
        line.delete(htf_support_line)
    if not na(htf_support_zone)
        box.delete(htf_support_zone)

    htf_support_line := line.new(bar_index - zone_extend, last_htf_swing_low,
                                bar_index + zone_extend, last_htf_swing_low,
                                color=color.green, style=line.style_dashed, width=2)

    htf_support_zone := box.new(bar_index - zone_extend, last_htf_swing_low - zone_tolerance_price,
                               bar_index + zone_extend, last_htf_swing_low + zone_tolerance_price,
                               bgcolor=color.new(color.green, 90), border_color=color.green, border_width=1)

// Check if price is in HTF zones
in_htf_resistance_zone = not na(last_htf_swing_high) and close >= (last_htf_swing_high - zone_tolerance_price) and close <= (last_htf_swing_high + zone_tolerance_price)
in_htf_support_zone = not na(last_htf_swing_low) and close >= (last_htf_swing_low - zone_tolerance_price) and close <= (last_htf_swing_low + zone_tolerance_price)

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📊 MARKET STRUCTURE ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Track previous swing points for structure
var float prev_ltf_high = na
var float prev_ltf_low = na
var float prev2_ltf_high = na
var float prev2_ltf_low = na

// Update structure tracking
if not na(ltf_swing_high)
    prev2_ltf_high := prev_ltf_high
    prev_ltf_high := ltf_swing_high
if not na(ltf_swing_low)
    prev2_ltf_low := prev_ltf_low
    prev_ltf_low := ltf_swing_low

// Market structure conditions
higher_high = not na(prev_ltf_high) and not na(prev2_ltf_high) and prev_ltf_high > prev2_ltf_high
higher_low = not na(prev_ltf_low) and not na(prev2_ltf_low) and prev_ltf_low > prev2_ltf_low
lower_high = not na(prev_ltf_high) and not na(prev2_ltf_high) and prev_ltf_high < prev2_ltf_high
lower_low = not na(prev_ltf_low) and not na(prev2_ltf_low) and prev_ltf_low < prev2_ltf_low

// Structure bias
bullish_structure = higher_high or higher_low
bearish_structure = lower_high or lower_low

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📈 PULLBACK ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Calculate pullback levels
pullback_level_high = not na(last_ltf_swing_high) ? last_ltf_swing_high - ((last_ltf_swing_high - last_ltf_swing_low) * pullback_min / 100) : na
pullback_level_low = not na(last_ltf_swing_low) ? last_ltf_swing_low + ((last_ltf_swing_high - last_ltf_swing_low) * pullback_min / 100) : na

pullback_level_high_max = not na(last_ltf_swing_high) ? last_ltf_swing_high - ((last_ltf_swing_high - last_ltf_swing_low) * pullback_max / 100) : na
pullback_level_low_max = not na(last_ltf_swing_low) ? last_ltf_swing_low + ((last_ltf_swing_high - last_ltf_swing_low) * pullback_max / 100) : na

// Valid pullback conditions
valid_pullback_long = not na(pullback_level_high) and not na(pullback_level_high_max) and
                     close >= pullback_level_high_max and close <= pullback_level_high and
                     close > ltf_ema_slow

valid_pullback_short = not na(pullback_level_low) and not na(pullback_level_low_max) and
                      close <= pullback_level_low_max and close >= pullback_level_low and
                      close < ltf_ema_slow

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🚦 ENTRY CONDITIONS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Long entry conditions (all must be true)
long_condition_1 = htf_bullish                    // HTF Bias: EMA 9 > EMA 21 on HTF
long_condition_2 = ltf_bullish                    // LTF Confirmation: EMA 9 > EMA 21 on LTF
long_condition_3 = bullish_structure              // LTF Structure: HH or HL formed
long_condition_4 = in_htf_support_zone           // HTF Zone: Price in Support/Demand zone
long_condition_5 = valid_pullback_long           // Valid Pullback: 38.2-61.8% + above EMA 21

long_entry = long_condition_1 and long_condition_2 and long_condition_3 and long_condition_4 and long_condition_5

// Short entry conditions (all must be true)
short_condition_1 = htf_bearish                   // HTF Bias: EMA 9 < EMA 21 on HTF
short_condition_2 = ltf_bearish                   // LTF Confirmation: EMA 9 < EMA 21 on LTF
short_condition_3 = bearish_structure             // LTF Structure: LL or LH formed
short_condition_4 = in_htf_resistance_zone       // HTF Zone: Price in Resistance/Supply zone
short_condition_5 = valid_pullback_short         // Valid Pullback: 38.2-61.8% + below EMA 21

short_entry = short_condition_1 and short_condition_2 and short_condition_3 and short_condition_4 and short_condition_5

// ═══════════════════════════════════════════════════════════════════════════════════════
// 💰 POSITION MANAGEMENT
// ═══════════════════════════════════════════════════════════════════════════════════════

// Calculate position size based on risk
calculate_position_size(entry_price, stop_loss) =>
    risk_amount = strategy.equity * risk_percent / 100
    price_diff = math.abs(entry_price - stop_loss)
    position_size = risk_amount / price_diff
    position_size

// Entry and exit logic
if long_entry and strategy.position_size == 0
    entry_price = close
    stop_loss = entry_price - (atr_value * atr_multiplier)
    take_profit = entry_price + ((entry_price - stop_loss) * risk_reward)

    pos_size = calculate_position_size(entry_price, stop_loss)
    strategy.entry("LONG", strategy.long, qty=pos_size)
    strategy.exit("LONG_EXIT", "LONG", stop=stop_loss, limit=take_profit)

if short_entry and strategy.position_size == 0
    entry_price = close
    stop_loss = entry_price + (atr_value * atr_multiplier)
    take_profit = entry_price - ((stop_loss - entry_price) * risk_reward)

    pos_size = calculate_position_size(entry_price, stop_loss)
    strategy.entry("SHORT", strategy.short, qty=pos_size)
    strategy.exit("SHORT_EXIT", "SHORT", stop=stop_loss, limit=take_profit)

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎨 VISUAL ELEMENTS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Plot EMAs
plot(ltf_ema_fast, "LTF EMA Fast", color=color.blue, linewidth=1)
plot(ltf_ema_slow, "LTF EMA Slow", color=color.orange, linewidth=1)

// Background colors for trend alignment
bgcolor_color = show_background ? (trend_aligned_bull ? color.new(color.green, 95) : trend_aligned_bear ? color.new(color.red, 95) : htf_bullish ? color.new(color.green, 98) : htf_bearish ? color.new(color.red, 98) : color.new(color.gray, 98)) : na

bgcolor(bgcolor_color, title="Trend Alignment Background")

// Labels for swing points and entries
if show_ltf_structure
    if higher_high
        label.new(bar_index, high, "HH", style=label.style_label_down, color=color.green, textcolor=color.white, size=size.small)
    if higher_low
        label.new(bar_index, low, "HL", style=label.style_label_up, color=color.green, textcolor=color.white, size=size.small)
    if lower_high
        label.new(bar_index, high, "LH", style=label.style_label_down, color=color.red, textcolor=color.white, size=size.small)
    if lower_low
        label.new(bar_index, low, "LL", style=label.style_label_up, color=color.red, textcolor=color.white, size=size.small)

// Entry signals
if long_entry
    label.new(bar_index, low, "LONG ENTRY", style=label.style_label_up, color=color.lime, textcolor=color.black, size=size.normal)
    alert("LONG ENTRY SIGNAL: All conditions met for long trade", alert.freq_once_per_bar)

if short_entry
    label.new(bar_index, high, "SHORT ENTRY", style=label.style_label_down, color=color.red, textcolor=color.white, size=size.normal)
    alert("SHORT ENTRY SIGNAL: All conditions met for short trade", alert.freq_once_per_bar)

// HTF zone labels
if not na(htf_swing_high) and show_htf_zones
    label.new(bar_index, htf_swing_high, "HTF-R", style=label.style_label_left, color=color.red, textcolor=color.white, size=size.small)

if not na(htf_swing_low) and show_htf_zones
    label.new(bar_index, htf_swing_low, "HTF-S", style=label.style_label_left, color=color.green, textcolor=color.white, size=size.small)

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📊 HELPER FUNCTIONS FOR TABLE
// ═══════════════════════════════════════════════════════════════════════════════════════

// Helper function to get status text and color
get_status(condition) =>
    if condition
        [color.green, "✅ Ready"]
    else
        [color.red, "❌ Wait"]

get_trend_status(bullish, bearish) =>
    if bullish
        [color.green, "🟢 Bullish"]
    else if bearish
        [color.red, "🔴 Bearish"]
    else
        [color.gray, "⚪ Neutral"]

get_alignment_status(aligned_bull, aligned_bear) =>
    if aligned_bull
        [color.green, "🟢 Bull Aligned"]
    else if aligned_bear
        [color.red, "🔴 Bear Aligned"]
    else
        [color.orange, "🟡 Not Aligned"]

get_zone_status(in_support, in_resistance) =>
    if in_support
        [color.green, "🟢 Support Zone"]
    else if in_resistance
        [color.red, "🔴 Resistance Zone"]
    else
        [color.gray, "⚪ No Zone"]

get_structure_status(bullish_struct, bearish_struct) =>
    if bullish_struct
        [color.green, "🟢 Bullish (HH/HL)"]
    else if bearish_struct
        [color.red, "🔴 Bearish (LL/LH)"]
    else
        [color.gray, "⚪ Neutral"]

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📊 INFORMATION TABLE
// ═══════════════════════════════════════════════════════════════════════════════════════

if show_info_table and barstate.islast
    // Create table
    var table info_table = table.new(position.top_right, 2, 10, bgcolor=color.new(color.white, 80), border_width=1)
            [color.green, "🟢 Bullish (HH/HL)"]
        else if bearish_struct
            [color.red, "🔴 Bearish (LL/LH)"]
        else
            [color.gray, "⚪ Neutral"]

    // Table headers
    table.cell(info_table, 0, 0, "📊 MULTI-TF ANALYSIS", text_color=color.white, bgcolor=color.new(color.blue, 20), text_size=size.normal)
    table.cell(info_table, 1, 0, "STATUS", text_color=color.white, bgcolor=color.new(color.blue, 20), text_size=size.normal)

    // HTF Bias
    [htf_color, htf_text] = get_trend_status(htf_bullish, htf_bearish)
    table.cell(info_table, 0, 1, "HTF Bias (1H)", text_color=color.black, bgcolor=color.new(color.white, 50))
    table.cell(info_table, 1, 1, htf_text, text_color=htf_color, bgcolor=color.new(color.white, 50))

    // LTF Trend
    [ltf_color, ltf_text] = get_trend_status(ltf_bullish, ltf_bearish)
    table.cell(info_table, 0, 2, "LTF Trend (5M)", text_color=color.black, bgcolor=color.new(color.white, 50))
    table.cell(info_table, 1, 2, ltf_text, text_color=ltf_color, bgcolor=color.new(color.white, 50))

    // Alignment
    [align_color, align_text] = get_alignment_status(trend_aligned_bull, trend_aligned_bear)
    table.cell(info_table, 0, 3, "Alignment", text_color=color.black, bgcolor=color.new(color.white, 50))
    table.cell(info_table, 1, 3, align_text, text_color=align_color, bgcolor=color.new(color.white, 50))

    // HTF Zones
    [zone_color, zone_text] = get_zone_status(in_htf_support_zone, in_htf_resistance_zone)
    table.cell(info_table, 0, 4, "HTF Zones", text_color=color.black, bgcolor=color.new(color.white, 50))
    table.cell(info_table, 1, 4, zone_text, text_color=zone_color, bgcolor=color.new(color.white, 50))

    // LTF Structure
    [struct_color, struct_text] = get_structure_status(bullish_structure, bearish_structure)
    table.cell(info_table, 0, 5, "LTF Structure", text_color=color.black, bgcolor=color.new(color.white, 50))
    table.cell(info_table, 1, 5, struct_text, text_color=struct_color, bgcolor=color.new(color.white, 50))

    // Pullback
    [pullback_long_color, pullback_long_text] = get_status(valid_pullback_long)
    [pullback_short_color, pullback_short_text] = get_status(valid_pullback_short)
    pullback_text = valid_pullback_long ? pullback_long_text : (valid_pullback_short ? pullback_short_text : "❌ No Valid Pullback")
    pullback_color = valid_pullback_long ? pullback_long_color : (valid_pullback_short ? pullback_short_color : color.red)
    table.cell(info_table, 0, 6, "Pullback", text_color=color.black, bgcolor=color.new(color.white, 50))
    table.cell(info_table, 1, 6, pullback_text, text_color=pullback_color, bgcolor=color.new(color.white, 50))

    // Long Setup
    [long_color, long_text] = get_status(long_entry)
    table.cell(info_table, 0, 7, "Long Setup", text_color=color.black, bgcolor=color.new(color.white, 50))
    table.cell(info_table, 1, 7, long_text, text_color=long_color, bgcolor=color.new(color.white, 50))

    // Short Setup
    [short_color, short_text] = get_status(short_entry)
    table.cell(info_table, 0, 8, "Short Setup", text_color=color.black, bgcolor=color.new(color.white, 50))
    table.cell(info_table, 1, 8, short_text, text_color=short_color, bgcolor=color.new(color.white, 50))

    // Method
    table.cell(info_table, 0, 9, "Method", text_color=color.black, bgcolor=color.new(color.blue, 80))
    table.cell(info_table, 1, 9, "HTF Bias + LTF Entry", text_color=color.white, bgcolor=color.new(color.blue, 80))

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📈 PERFORMANCE METRICS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Plot performance metrics
plot(strategy.equity, "Equity", color=color.blue, display=display.data_window)
plot(strategy.max_drawdown, "Max Drawdown", color=color.red, display=display.data_window)

// Calculate win rate
total_trades = strategy.closedtrades
winning_trades = strategy.wintrades
win_rate = total_trades > 0 ? (winning_trades / total_trades) * 100 : 0

// Display key metrics in data window
plotchar(win_rate, "Win Rate %", "", location.top, color.blue, display=display.data_window)
plotchar(strategy.netprofit, "Net Profit", "", location.top, color.green, display=display.data_window)
plotchar(risk_percent, "Risk per Trade %", "", location.top, color.orange, display=display.data_window)
plotchar(risk_reward, "Risk:Reward", "", location.top, color.purple, display=display.data_window)

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔔 ALERTS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Trend alignment alerts
alertcondition(trend_aligned_bull and not trend_aligned_bull[1], "Bullish Alignment", "HTF and LTF trends are now aligned bullish")
alertcondition(trend_aligned_bear and not trend_aligned_bear[1], "Bearish Alignment", "HTF and LTF trends are now aligned bearish")

// Zone entry alerts
alertcondition(in_htf_support_zone and not in_htf_support_zone[1], "Entered HTF Support Zone", "Price has entered HTF Support/Demand zone")
alertcondition(in_htf_resistance_zone and not in_htf_resistance_zone[1], "Entered HTF Resistance Zone", "Price has entered HTF Resistance/Supply zone")

// Structure alerts
alertcondition(higher_high, "Higher High Formed", "New Higher High formed on LTF - Bullish structure")
alertcondition(higher_low, "Higher Low Formed", "New Higher Low formed on LTF - Bullish structure")
alertcondition(lower_high, "Lower High Formed", "New Lower High formed on LTF - Bearish structure")
alertcondition(lower_low, "Lower Low Formed", "New Lower Low formed on LTF - Bearish structure")

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📝 STRATEGY SUMMARY
// ═══════════════════════════════════════════════════════════════════════════════════════

// Strategy description for documentation
// This HTF Zone Mapper implements a multi-timeframe trading strategy that:
// 1. Uses HTF (1H) for market bias and zone identification
// 2. Uses LTF (5M) for precise entry timing
// 3. Requires all 5 conditions to be met for entry:
//    - HTF bias alignment (EMA 9 vs 21)
//    - LTF confirmation (EMA 9 vs 21)
//    - LTF market structure (HH/HL for long, LL/LH for short)
//    - HTF zone confluence (Support for long, Resistance for short)
//    - Valid pullback (38.2-61.8% retracement)
// 4. Implements proper risk management with ATR-based stops
// 5. Provides comprehensive visual feedback and alerts